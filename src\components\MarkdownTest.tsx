import React from 'react';
import MarkdownRenderer from './MarkdownRenderer';

const MarkdownTest: React.FC = () => {
  const testContent = `# Markdown Test

This is a test of the markdown renderer with various features:

## Lists and Formatting

Here's a numbered list:
1. First item with **bold text**
2. Second item with *italic text*
3. Third item with \`inline code\`
4. Fourth item with [a link](https://example.com)

And a bullet list:
- Item one
- Item two with **bold**
- Item three with *italic*

## Code Blocks

Here's some JavaScript code:

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to AnyDocAI!\`;
}

greet("User");
\`\`\`

## Tables

| Feature | Status | Notes |
|---------|--------|-------|
| Markdown | ✅ | Fully supported |
| Code highlighting | ✅ | With syntax highlighting |
| Charts | ✅ | Via recharts |
| Tables | ✅ | GitHub flavored markdown |

## Charts

Here's a sample chart:

\`\`\`chart
{
  "type": "line",
  "data": [
    {"month": "Jan", "sales": 100, "profit": 20},
    {"month": "Feb", "sales": 150, "profit": 35},
    {"month": "Mar", "sales": 120, "profit": 25},
    {"month": "Apr", "sales": 180, "profit": 45}
  ]
}
\`\`\`

## Blockquotes

> This is a blockquote example.
> It can span multiple lines and provides
> a nice way to highlight important information.

## Mixed Content

The AI can now properly format responses with:
- **Bold text** for emphasis
- *Italic text* for subtle emphasis
- \`code snippets\` for technical terms
- Links to [external resources](https://example.com)
- Proper line breaks and paragraphs

This should solve the formatting issue where text appeared as one long line during streaming but formatted correctly after refresh.`;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Markdown Renderer Test</h1>
      <div className="border border-gray-200 rounded-lg p-4 bg-white">
        <MarkdownRenderer content={testContent} />
      </div>
    </div>
  );
};

export default MarkdownTest;
